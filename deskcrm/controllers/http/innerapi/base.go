package innerapi

import (
	"github.com/gin-gonic/gin"
)

func RegisterHandlers(rg *gin.RouterGroup) {
	innerapiRouter := rg.Group("/innerapi")
	apiRouter := rg.Group("/api")
	// ------------------------------------------------api router-------------------------------------------------------
	courseApiGroup := apiRouter.Group("/course/")
	{
		courseApiGroup.POST("courselistandcardbyyear", CourseController.CourseListAndCardByYearAPI)
		courseApiGroup.POST("getLessonplayinfolist", CourseController.GetLessonPlayInfoList)
		courseApiGroup.GET("allowautocallandmessage", CourseController.AllowAutoCallAndMessage)
	}

	apiFilterGroup := apiRouter.Group("/filter/")
	{
		apiFilterGroup.POST("getfilterconfig", ArkController.GetFilterConfigAPI)
		apiFilterGroup.GET("getfilterconfig", ArkController.GetFilterConfigAPI)
		apiFilterGroup.POST("studentlist", StudentController.GetStudentListAPI)
	}

	apiTaskGroup := apiRouter.Group("/task/")
	{
		apiTaskGroup.POST("lessoninfo", LessonController.GetLessonListAPI)
	}

	toolGroup := apiRouter.Group("/tool/")
	{
		toolGroup.GET("startTask", ToolController.StartUpdateHandler)
		toolGroup.GET("stopTask", ToolController.StopUpdateHandler)
	}

	keepDetailGroup := innerapiRouter.Group("/keepdetail/")
	{
		keepDetailGroup.GET("lessonlist", KeepDetailController.LessonList)
	}
}
