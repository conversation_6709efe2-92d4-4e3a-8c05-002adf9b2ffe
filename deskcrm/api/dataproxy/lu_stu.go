package dataproxy

import "github.com/spf13/cast"

// GetLuDataEsResp ES接口返回的原始数据结构（使用ES字段名）
// 根据PHP initLuData方法中的fields数组精简，只保留实际使用的字段
type GetLuDataEsResp struct {
	// 基础信息字段 (ES字段名)
	LessonId       int64 `json:"lessonId" mapstructure:"lessonId"`
	TradeStatus    int64 `json:"trade_status" mapstructure:"trade_status"`
	MicNum         int64 `json:"mic_num" mapstructure:"mic_num"`
	ChatNum        int64 `json:"chat_num" mapstructure:"chat_num"`
	PraiseNum      int64 `json:"praise_num" mapstructure:"praise_num"`           // 表扬次数
	MainDepartment int64 `json:"main_department" mapstructure:"main_department"` // 主部门

	// 到课相关字段 (ES字段名)
	AttendDuration          int64 `json:"attend_duration" mapstructure:"attend_duration"`
	IsAttendFinish          int64 `json:"is_attend_finish" mapstructure:"is_attend_finish"`
	PreclassAttendDuration  int64 `json:"preclass_attend_duration" mapstructure:"preclass_attend_duration"`
	PostclassAttendDuration int64 `json:"postclass_attend_duration" mapstructure:"postclass_attend_duration"`
	Attend                  int64 `json:"attend" mapstructure:"attend"`
	IsAiAttend              int64 `json:"is_ai_attend" mapstructure:"is_ai_attend"`
	IsLbpAttend             int64 `json:"is_lbp_attend" mapstructure:"is_lbp_attend"`
	IsAiFinish              int64 `json:"is_ai_finish" mapstructure:"is_ai_finish"`
	IsLbpAttendFinish       int64 `json:"is_lbp_attend_finish" mapstructure:"is_lbp_attend_finish"`

	// 录播相关字段 (ES字段名)
	PlaybackTime                          int64 `json:"playback_time" mapstructure:"playback_time"`
	PlaybackTimeIn7d                      int64 `json:"playback_time_in_7d" mapstructure:"playback_time_in_7d"`
	PlaybackTimeAfterUnlock               int64 `json:"playback_time_after_unlock" mapstructure:"playback_time_after_unlock"`
	PlaybackTimeAfterUnlock7d             int64 `json:"playback_time_after_unlock_7d" mapstructure:"playback_time_after_unlock_7d"`
	PlaybackParticipateNum                int64 `json:"playback_participate_num" mapstructure:"playback_participate_num"`
	PlaybackRightNum                      int64 `json:"playback_right_num" mapstructure:"playback_right_num"`
	InclassTeacherRoomTotalPlaybackTimeV1 int64 `json:"inclass_teacher_room_total_playback_time_v1" mapstructure:"inclass_teacher_room_total_playback_time_v1"`

	// 考试答案详情 (ES字段名)
	ExamAnswer map[string]map[string]interface{} `json:"exam_answer" mapstructure:"exam_answer"`

	// LBP相关字段 (ES字段名)
	LbpAttendDuration       int64 `json:"lbp_attend_duration" mapstructure:"lbp_attend_duration"`
	LbpInteractionRightNum  int64 `json:"lbp_interaction_right_num" mapstructure:"lbp_interaction_right_num"`
	LbpInteractionSubmitNum int64 `json:"lbp_interaction_submit_num" mapstructure:"lbp_interaction_submit_num"`

	// 融合课相关字段 (ES字段名)
	MixLiveInteractionRightNum      int64 `json:"mix_live_interaction_right_num" mapstructure:"mix_live_interaction_right_num"`
	MixLiveInteractionSubmitNum     int64 `json:"mix_live_interaction_submit_num" mapstructure:"mix_live_interaction_submit_num"`
	MixPlaybackInteractionRightNum  int64 `json:"mix_playback_interaction_right_num" mapstructure:"mix_playback_interaction_right_num"`
	MixPlaybackInteractionSubmitNum int64 `json:"mix_playback_interaction_submit_num" mapstructure:"mix_playback_interaction_submit_num"`

	// 小鹿编程相关字段 (ES字段名)
	DeerProgrammingHomeworkLevel  int64  `json:"deer_programming_homework_level" mapstructure:"deer_programming_homework_level"`
	DeerProgrammingHomeworkStatus string `json:"deer_programming_homework_status" mapstructure:"deer_programming_homework_status"`

	// 浣熊英语相关字段 (ES字段名)
	HxPretestFinishnum int64 `json:"hx_pretest_finishnum" mapstructure:"hx_pretest_finishnum"`
}

// GetLuDataResp 精简版响应结构体，只包含PHP initLuData方法中实际使用的字段
type GetLuDataResp struct {
	// ==================== 基础信息模块 (Basic Info) ====================
	LessonId       int64 `json:"lessonId" mapstructure:"lessonId"`               // 课节ID
	TradeStatus    int64 `json:"trade_status" mapstructure:"trade_status"`       // 交易状态
	MicNum         int64 `json:"micNum" mapstructure:"micNum"`                   // 麦克风次数
	ChatNum        int64 `json:"chatNum" mapstructure:"chatNum"`                 // 聊天次数
	PraiseNum      int64 `json:"praiseNum" mapstructure:"praiseNum"`             // 表扬次数
	MainDepartment int64 `json:"main_department" mapstructure:"main_department"` // 主部门 (兼容字段)

	// ==================== 到课相关模块 (Attendance) ====================
	AttendDuration          int64 `json:"attendDuration"`          // 到课时长
	IsAttendFinish          int64 `json:"isAttendFinish"`          // 是否完课
	PreclassAttendDuration  int64 `json:"preclassAttendDuration"`  // 课前到课时长
	PostclassAttendDuration int64 `json:"postclassAttendDuration"` // 课后到课时长
	Attend                  int64 `json:"attend"`                  // 到课状态
	IsAiAttend              int64 `json:"isAiAttend"`              // AI到课状态
	IsLbpAttend             int64 `json:"isLbpAttend"`             // LBP到课状态
	IsAiFinish              int64 `json:"isAiFinish"`              // AI完课状态
	IsLbpAttendFinish       int64 `json:"isLbpAttendFinish"`       // LBP完课状态

	// ==================== 录播相关模块 (Playback) ====================
	PlaybackTotalTime                     int64 `json:"playbackTotalTime"`                                                                                      // 录播总时长
	PlaybackTimeIn7d                      int64 `json:"playbackTimeIn7d"`                                                                                       // 7天内录播时长
	PlaybackTimeAfterUnlock               int64 `json:"playbackTimeAfterUnlock"`                                                                                // 解锁后录播时长
	PlaybackTimeAfterUnlock7d             int64 `json:"playbackTimeAfterUnlock7d"`                                                                              // 解锁后7天内录播时长
	PlaybackParticipateNum                int64 `json:"playbackParticipateNum"`                                                                                 // 回放参与数
	PlaybackRightNum                      int64 `json:"playbackRightNum"`                                                                                       // 回放正确数
	InclassTeacherRoomTotalPlaybackTimeV1 int64 `json:"inclass_teacher_room_total_playback_time_v1" mapstructure:"inclass_teacher_room_total_playback_time_v1"` // 回放时长(新)

	// ==================== 考试相关模块 (Exams) ====================
	ExamAnswer map[string]map[string]interface{} `json:"exam_answer"` // 考试答案详情

	// 互动题 (exam1)
	ExerciseRightNum       int64 `json:"exerciseRightNum"`       // 互动题正确数
	ExerciseParticipateNum int64 `json:"exerciseParticipateNum"` // 互动题参与数
	ExerciseTotalNum       int64 `json:"exerciseTotalNum"`       // 互动题总数 (兼容字段)

	// 预习相关字段
	IsPreviewFinish       int64 `json:"isPreviewFinish"`       // 预习是否完成
	PreviewParticipateNum int64 `json:"previewParticipateNum"` // 预习参与数
	PreviewCorrectNum     int64 `json:"previewCorrectNum"`     // 预习正确数
	PreviewTotalNum       int64 `json:"previewTotalNum"`       // 预习总数
	PreviewTotalNum5      int64 `json:"previewTotalNum5"`      // 小学预习总数 (兼容字段)
	PreviewTotalNum13     int64 `json:"previewTotalNum13"`     // 初高中预习总数 (兼容字段)

	// 巩固练习 (exam7)
	HomeworkLevel                  int64 `json:"homeworkLevel"`                  // 巩固练习等级
	HomeworkPracticeParticipateNum int64 `json:"homeworkPracticeParticipateNum"` // 巩固练习参与数
	HomeworkPracticeCorrectNum     int64 `json:"homeworkPracticeCorrectNum"`     // 巩固练习正确数
	HomeworkPracticeTotalNum       int64 `json:"homeworkPracticeTotalNum"`       // 巩固练习总数 (兼容字段)

	// ilab巩固练习 (兼容字段)
	IlabHomeworkGeXingTotalNum int64 `json:"ilabHomeworkGeXingTotalNum"` // ilab巩固练习总数

	// 阶段测 (兼容字段)
	StageTestExamTotalNum int64 `json:"stageTestExamTotalNum"` // 阶段测总数

	// 堂堂测 (exam10)
	IsTangTangExamSubmit       int64 `json:"isTangTangExamSubmit"`       // 堂堂测是否提交
	TangTangExamParticipateNum int64 `json:"tangTangExamParticipateNum"` // 堂堂测参与数
	TangTangExamCorrectNum     int64 `json:"tangTangExamCorrectNum"`     // 堂堂测正确数
	TangTangExamScore          int64 `json:"tangTangExamScore"`          // 堂堂测分数
	TangTangExamTotalNum       int64 `json:"tangTangExamTotalNum"`       // 堂堂测总数 (兼容字段)

	// 同步练习 (exam11)
	SynchronousPracticeParticipateNum int64 `json:"synchronousPracticeParticipateNum"` // 同步练习参与数
	SynchronousPracticeCorrectNum     int64 `json:"synchronousPracticeCorrectNum"`     // 同步练习正确数
	SynchronousPracticeTotalNum       int64 `json:"synchronousPracticeTotalNum"`       // 同步练习总数 (兼容字段)

	// 口述题 (exam32)
	OralQuestionSubmit      int64 `json:"oralQuestionSubmit"`      // 口述题是否提交
	OralQuestionCorrectTime int64 `json:"oralQuestionCorrectTime"` // 口述题批改时间
	OralQuestionTotalNum    int64 `json:"oralQuestionTotalNum"`    // 口述题总数 (兼容字段)
	HaveOralQuestion        int64 `json:"haveOralQuestion"`        // 是否有口述题 (兼容字段)

	// 帮帮英语相关字段 (兼容字段)
	WordpracticeTotalNum    int64 `json:"wordpracticeTotalNum"`    // 单词练习总数
	WordlearnTotalNum       int64 `json:"wordlearnTotalNum"`       // 单词学习总数
	OutTestTotalNum         int64 `json:"outTestTotalNum"`         // 出门测总数
	ImproveTotalNum         int64 `json:"improveTotalNum"`         // 提升训练总数
	GrammarpracticeTotalNum int64 `json:"grammarpracticeTotalNum"` // 语法练习总数
	PracticesTotalNum       int64 `json:"practicesTotalNum"`       // 练一练总数
	InTestTotalNum          int64 `json:"inTestTotalNum"`          // 入门测总数

	// ==================== 英语专项模块 (English) ====================
	// 浣熊英语 - 小试牛刀 (exam211)
	TryKnifeScore int64 `json:"tryKnifeScore"` // 小试牛刀分数

	// 浣熊英语 - lesson1 (exam212)
	LessonOneScore        int64 `json:"lessonOneScore"`        // lesson1分数
	LessonOneSubmitStatus int64 `json:"lessonOneSubmitStatus"` // lesson1提交状态

	// 浣熊英语 - lesson2 (exam212)
	LessonTwoScore        int64 `json:"lessonTwoScore"`        // lesson2分数
	LessonTwoSubmitStatus int64 `json:"lessonTwoSubmitStatus"` // lesson2提交状态

	// 浣熊英语 - lesson3 (exam212)
	LessonThreeScore        int64 `json:"lessonThreeScore"`        // lesson3分数
	LessonThreeSubmitStatus int64 `json:"lessonThreeSubmitStatus"` // lesson3提交状态

	// 浣熊英语 - 综合演练 (exam216)
	SynthesisManoeuvreScore        int64 `json:"synthesisManoeuvreScore"`        // 综合演练分数
	SynthesisManoeuvreSubmitStatus int64 `json:"synthesisManoeuvreSubmitStatus"` // 综合演练提交状态

	// 浣熊英语 - 能力挑战 (exam213)
	PowerChallengeScore        int64 `json:"powerChallengeScore"`        // 能力挑战分数
	PowerChallengeSubmitStatus int64 `json:"powerChallengeSubmitStatus"` // 能力挑战提交状态

	// 浣熊英语 - 综合秀场 (exam217)
	SynthesisShowScore        int64 `json:"synthesisShowScore"`        // 综合秀场分数
	SynthesisShowSubmitStatus int64 `json:"synthesisShowSubmitStatus"` // 综合秀场提交状态

	// 浣熊英语 - 测试完成数量
	HxPretestFinishnum int64 `json:"hxPretestFinishnum"` // 浣熊课前测试题的完成数量

	// ==================== LBP相关模块 (LBP) ====================
	LbpAttendDuration               int64 `json:"lbpAttendDuration"`               // LBP观看时长
	LbpInteractionRightNum          int64 `json:"lbpInteractionRightNum"`          // LBP课中互动题正确数
	LbpInteractionSubmitNum         int64 `json:"lbpInteractionSubmitNum"`         // LBP课中互动题提交数
	MixLiveInteractionRightNum      int64 `json:"mixLiveInteractionRightNum"`      // LBP互动题正确数
	MixLiveInteractionSubmitNum     int64 `json:"mixLiveInteractionSubmitNum"`     // LBP互动题提交数
	MixPlaybackInteractionRightNum  int64 `json:"mixPlaybackInteractionRightNum"`  // 融合回放互动题正确数
	MixPlaybackInteractionSubmitNum int64 `json:"mixPlaybackInteractionSubmitNum"` // 融合回放互动题提交数

	// ==================== 小鹿编程模块 (Deer Programming) ====================
	DeerProgrammingHomeworkLevel  int64  `json:"deerProgrammingHomeworkLevel"`  // 小鹿编程作业等级
	DeerProgrammingHomeworkStatus string `json:"deerProgrammingHomeworkStatus"` // 小鹿编程作业状态
}

// ConvertEsRespToCommonResp 将ES返回的原始数据转换为通用响应格式
func ConvertEsRespToCommonResp(esResp *GetLuDataEsResp) *GetLuDataResp {
	if esResp == nil {
		return nil
	}

	resp := &GetLuDataResp{
		// 基础信息字段映射
		LessonId:       esResp.LessonId,
		TradeStatus:    esResp.TradeStatus,
		MicNum:         esResp.MicNum,
		ChatNum:        esResp.ChatNum,
		PraiseNum:      esResp.PraiseNum,
		MainDepartment: esResp.MainDepartment,

		// 到课相关字段映射
		AttendDuration:          esResp.AttendDuration,
		IsAttendFinish:          esResp.IsAttendFinish,
		PreclassAttendDuration:  esResp.PreclassAttendDuration,
		PostclassAttendDuration: esResp.PostclassAttendDuration,
		Attend:                  esResp.Attend,
		IsAiAttend:              esResp.IsAiAttend,
		IsLbpAttend:             esResp.IsLbpAttend,
		IsAiFinish:              esResp.IsAiFinish,
		IsLbpAttendFinish:       esResp.IsLbpAttendFinish,

		// 录播相关字段映射
		PlaybackTotalTime:                     esResp.PlaybackTime, // ES字段名映射
		PlaybackTimeIn7d:                      esResp.PlaybackTimeIn7d,
		PlaybackTimeAfterUnlock:               esResp.PlaybackTimeAfterUnlock,
		PlaybackTimeAfterUnlock7d:             esResp.PlaybackTimeAfterUnlock7d,
		PlaybackParticipateNum:                esResp.PlaybackParticipateNum,
		PlaybackRightNum:                      esResp.PlaybackRightNum,
		InclassTeacherRoomTotalPlaybackTimeV1: esResp.InclassTeacherRoomTotalPlaybackTimeV1,

		// 考试相关字段映射
		ExamAnswer: esResp.ExamAnswer,

		// LBP相关字段映射
		LbpAttendDuration:               esResp.LbpAttendDuration,
		LbpInteractionRightNum:          esResp.LbpInteractionRightNum,
		LbpInteractionSubmitNum:         esResp.LbpInteractionSubmitNum,
		MixLiveInteractionRightNum:      esResp.MixLiveInteractionRightNum,
		MixLiveInteractionSubmitNum:     esResp.MixLiveInteractionSubmitNum,
		MixPlaybackInteractionRightNum:  esResp.MixPlaybackInteractionRightNum,
		MixPlaybackInteractionSubmitNum: esResp.MixPlaybackInteractionSubmitNum,

		// 小鹿编程相关字段映射
		DeerProgrammingHomeworkLevel:  esResp.DeerProgrammingHomeworkLevel,
		DeerProgrammingHomeworkStatus: esResp.DeerProgrammingHomeworkStatus,

		// 浣熊英语相关字段映射
		HxPretestFinishnum: esResp.HxPretestFinishnum,
	}

	// 从ExamAnswer中提取具体的考试字段
	if esResp.ExamAnswer != nil {
		resp.extractExamFields(esResp.ExamAnswer)
	}

	return resp
}

// extractExamFields 从ExamAnswer中提取具体的考试字段
func (resp *GetLuDataResp) extractExamFields(examAnswer map[string]map[string]interface{}) {
	// 互动题 (exam1)
	if exam1, ok := examAnswer["exam1"]; ok {
		if val, exists := exam1["right_num"]; exists {
			resp.ExerciseRightNum = cast.ToInt64(val)
		}
		if val, exists := exam1["participate_num"]; exists {
			resp.ExerciseParticipateNum = cast.ToInt64(val)
		}
	}

	// 课前预习 (exam5) - 映射到通用预习字段
	if exam5, ok := examAnswer["exam5"]; ok {
		if val, exists := exam5["is_finish"]; exists {
			resp.IsPreviewFinish = cast.ToInt64(val)
		}
		if val, exists := exam5["participate_num"]; exists {
			resp.PreviewParticipateNum = cast.ToInt64(val)
		}
		if val, exists := exam5["right_num"]; exists {
			resp.PreviewCorrectNum = cast.ToInt64(val)
		}
		if val, exists := exam5["total_num"]; exists {
			resp.PreviewTotalNum = cast.ToInt64(val)
		}
	}

	// 课前预习 (exam13) - 初中高中预习，映射到通用预习字段
	if exam13, ok := examAnswer["exam13"]; ok {
		if val, exists := exam13["is_finish"]; exists {
			resp.IsPreviewFinish = cast.ToInt64(val)
		}
		if val, exists := exam13["participate_num"]; exists {
			resp.PreviewParticipateNum = cast.ToInt64(val)
		}
		if val, exists := exam13["right_num"]; exists {
			resp.PreviewCorrectNum = cast.ToInt64(val)
		}
		if val, exists := exam13["total_num"]; exists {
			resp.PreviewTotalNum = cast.ToInt64(val)
		}
	}

	// 巩固练习 (exam7)
	if exam7, ok := examAnswer["exam7"]; ok {
		if val, exists := exam7["participate_num"]; exists {
			resp.HomeworkPracticeParticipateNum = cast.ToInt64(val)
		}
		if val, exists := exam7["right_num"]; exists {
			resp.HomeworkPracticeCorrectNum = cast.ToInt64(val)
		}
		if val, exists := exam7["correct_level"]; exists {
			resp.HomeworkLevel = cast.ToInt64(val)
		}
	}

	// 堂堂测 (exam10)
	if exam10, ok := examAnswer["exam10"]; ok {
		if val, exists := exam10["is_submit"]; exists {
			resp.IsTangTangExamSubmit = cast.ToInt64(val)
		}
		if val, exists := exam10["participate_num"]; exists {
			resp.TangTangExamParticipateNum = cast.ToInt64(val)
		}
		if val, exists := exam10["right_num"]; exists {
			resp.TangTangExamCorrectNum = cast.ToInt64(val)
		}
		if val, exists := exam10["answer_score"]; exists {
			resp.TangTangExamScore = cast.ToInt64(val)
		}
	}

	// 口述题 (exam32)
	if exam32, ok := examAnswer["exam32"]; ok {
		if val, exists := exam32["is_submit"]; exists {
			resp.OralQuestionSubmit = cast.ToInt64(val)
		}
		if val, exists := exam32["correct_time"]; exists {
			resp.OralQuestionCorrectTime = cast.ToInt64(val)
		}
	}

	// 同步练习 (exam11)
	if exam11, ok := examAnswer["exam11"]; ok {
		if val, exists := exam11["participate_num"]; exists {
			resp.SynchronousPracticeParticipateNum = cast.ToInt64(val)
		}
		if val, exists := exam11["right_num"]; exists {
			resp.SynchronousPracticeCorrectNum = cast.ToInt64(val)
		}
	}

	// 浣熊英语 - 小试牛刀 (exam211)
	if exam211, ok := examAnswer["exam211"]; ok {
		if val, exists := exam211["score"]; exists {
			resp.TryKnifeScore = cast.ToInt64(val)
		}
	}

	// 浣熊英语 - lesson1/2/3 (exam212)
	if exam212, ok := examAnswer["exam212"]; ok {
		if val, exists := exam212["lesson1_score"]; exists {
			resp.LessonOneScore = cast.ToInt64(val)
		}
		if val, exists := exam212["lesson1_submit_status"]; exists {
			resp.LessonOneSubmitStatus = cast.ToInt64(val)
		}
		if val, exists := exam212["lesson2_score"]; exists {
			resp.LessonTwoScore = cast.ToInt64(val)
		}
		if val, exists := exam212["lesson2_submit_status"]; exists {
			resp.LessonTwoSubmitStatus = cast.ToInt64(val)
		}
		if val, exists := exam212["lesson3_score"]; exists {
			resp.LessonThreeScore = cast.ToInt64(val)
		}
		if val, exists := exam212["lesson3_submit_status"]; exists {
			resp.LessonThreeSubmitStatus = cast.ToInt64(val)
		}
	}

	// 浣熊英语 - 能力挑战 (exam213)
	if exam213, ok := examAnswer["exam213"]; ok {
		if val, exists := exam213["score"]; exists {
			resp.PowerChallengeScore = cast.ToInt64(val)
		}
		if val, exists := exam213["submit_status"]; exists {
			resp.PowerChallengeSubmitStatus = cast.ToInt64(val)
		}
	}

	// 浣熊英语 - 综合演练 (exam216)
	if exam216, ok := examAnswer["exam216"]; ok {
		if val, exists := exam216["score"]; exists {
			resp.SynthesisManoeuvreScore = cast.ToInt64(val)
		}
		if val, exists := exam216["submit_status"]; exists {
			resp.SynthesisManoeuvreSubmitStatus = cast.ToInt64(val)
		}
	}

	// 浣熊英语 - 综合秀场 (exam217)
	if exam217, ok := examAnswer["exam217"]; ok {
		if val, exists := exam217["score"]; exists {
			resp.SynthesisShowScore = cast.ToInt64(val)
		}
		if val, exists := exam217["submit_status"]; exists {
			resp.SynthesisShowSubmitStatus = cast.ToInt64(val)
		}
	}
}
