package dat

import (
	"deskcrm/api/zbcore"
	"deskcrm/conf"
	"deskcrm/util"
	"errors"

	"git.zuoyebang.cc/fwyybase/fwyylibs/fwyyutils"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const (
	BatchSize4PerReq = 50 // 单次请求最大id数量

	Module = "dat"
	Entity = "courseTeacher"
)

// courseTeacher接口参数
type CourseTeacherParam struct {
	CourseIds []int64 `json:"courseIds"`
}

type TeacherUidList struct {
	TeacherUids []int `json:"teacherUids"`
}

// LessonTeacher 章节教师关系
type LessonTeacher struct {
	LessonId   int64 `json:"lessonId"`
	TeacherUid int   `json:"teacherUid"`
	Status     int   `json:"status"`
}

// 获取teacherUid list
// return: map[courseId]TeacherUidList
func GetTeachersByCourseIds(ctx *gin.Context, courseIds []int64, fields []string) (map[string]TeacherUidList, error) {
	if len(courseIds) == 0 {
		return nil, errors.New("param error")
	}

	if len(fields) == 0 { // default fields
		fields = util.GetJsonFieldNames(TeacherUidList{})
	} else {
		if isMatch, _ := fwyyutils.InArrayString("teacherUid", fields); !isMatch {
			fields = append(fields, "teacherUids")
		}
	}

	if len(courseIds) > BatchSize4PerReq {
		return util.BatchCall(ctx, courseIds, 10, getKVByCourseId, fields)
	} else {
		return getKVByCourseId(ctx, courseIds)
	}
}

// 根据课程id获取teacherUid list
// args: 可变参数数组中的第一个元素为fields []string
// return: map[courseId]TeacherUidList
func getKVByCourseId(ctx *gin.Context, courseIds []int64, args ...any) (rsp map[string]TeacherUidList, err error) {
	app := conf.GetAppName()
	arrHeader := zbcore.GetHeaders(zbcore.ServiceUri, Module, Entity, "getKV", false, app)
	arrParams := map[string]interface{}{
		"courseIds": courseIds,
		"fields":    []string{"teacherUids"}, // 目前字段较少, 出于性能考虑暂时写死
		//"fields": util.GetJsonFieldNames(TeacherUidList{}), // 日后若TeacherUidList字段较多或经常变动则用此方法
	}

	// 查询
	zlog.Debugf(ctx, "arrParams:%+v", arrParams)
	rsp = make(map[string]TeacherUidList)
	apiRsp, err := zbcore.PostDat(ctx, arrParams, arrHeader, &rsp)
	zlog.Debugf(ctx, "apiRsp:%+v", apiRsp)
	if err != nil {
		zlog.Errorf(ctx, "getKVByCourseId:%+v,%+v", apiRsp, err)
		return nil, err
	}

	return rsp, nil
}

// GetTeacherUidByLessonIdArr 根据章节ID获取教师UID
// 对应PHP的 Zb_Service_Dat_TeacherLesson::getTeacherUidByLessonIdArr 方法
func GetTeacherUidByLessonIdArr(ctx *gin.Context, lessonIds []int64, fields []string) ([]LessonTeacher, error) {
	if len(lessonIds) == 0 {
		return nil, errors.New("param error")
	}

	if len(fields) == 0 { // default fields
		fields = []string{"lessonId", "teacherUid", "status"}
	}

	app := conf.GetAppName()
	arrHeader := zbcore.GetHeaders(zbcore.ServiceUri, "dat", "teacherlesson", "getTeacherUidByLessonIdArr", false, app)
	arrParams := map[string]interface{}{
		"lessonIds": lessonIds,
		"fields":    fields,
	}

	var output map[string]interface{}
	apiResp, err := zbcore.PostDal(ctx, arrParams, arrHeader, &output)
	zlog.Debugf(ctx, "GetTeacherUidByLessonIdArr apiResp:%+v", apiResp)
	if err != nil {
		zlog.Errorf(ctx, "dat api error, err:%s", err.Error())
		return nil, err
	}

	// 解析响应数据
	var lessonTeachers []LessonTeacher
	if data, exists := output["data"]; exists {
		if dataArray, ok := data.([]interface{}); ok {
			for _, item := range dataArray {
				if itemMap, ok := item.(map[string]interface{}); ok {
					var lessonTeacher LessonTeacher
					if lessonId, ok := itemMap["lessonId"].(float64); ok {
						lessonTeacher.LessonId = int64(lessonId)
					}
					if teacherUid, ok := itemMap["teacherUid"].(float64); ok {
						lessonTeacher.TeacherUid = int(teacherUid)
					}
					if status, ok := itemMap["status"].(float64); ok {
						lessonTeacher.Status = int(status)
					}
					lessonTeachers = append(lessonTeachers, lessonTeacher)
				}
			}
		}
	}

	return lessonTeachers, nil
}
