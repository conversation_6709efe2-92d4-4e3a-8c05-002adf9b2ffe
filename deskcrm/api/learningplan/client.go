package learningplan

import (
	"deskcrm/api"
	"deskcrm/conf"
	"deskcrm/libs/utils"
	"strconv"
	"strings"

	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

type Client struct {
	cli *base.ApiClient
}

// NewClient create Client instance
func NewClient() *Client {
	c := &Client{
		cli: conf.API.LearningPlan,
	}
	return c
}

const (
	chapterRequiredApi = "/learnplan-go/v1/assistantdesk/chapter_required"
	learningPlansApi   = "/learnplan-go/v1/assistantdesk/learnplans"
)

// GetStudChapStat 获取学生章节必看状态
// 对应PHP的 Api_LearningPlan::getStudChapStat 方法
func (c *Client) GetStudChapStat(ctx *gin.Context, studentUid int64, courseIds []int64) ([]ChapterStat, error) {
	// 参数验证
	if studentUid <= 0 || len(courseIds) == 0 {
		zlog.Warnf(ctx, "GetStudChapStat invalid params: studentUid=%d, courseIds=%v", studentUid, courseIds)
		return nil, nil
	}

	// 将 courseIds 转换为逗号分隔的字符串
	courseIdStrs := make([]string, len(courseIds))
	for i, id := range courseIds {
		courseIdStrs[i] = strconv.FormatInt(id, 10)
	}
	courseIdsStr := strings.Join(courseIdStrs, ",")

	// 构建请求参数
	req := map[string]interface{}{
		"student_id": studentUid,
		"course_ids": courseIdsStr,
	}

	// 设置请求选项
	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeJson}
	utils.DecorateHttpOptions(ctx, &opts)

	// 发送请求
	res, err := c.cli.HttpPost(ctx, chapterRequiredApi, opts)
	if err != nil {
		zlog.Warnf(ctx, "GetStudChapStat request err: %v", err)
		return nil, err
	}

	// 解析响应
	var resp Chapter
	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return nil, err
	}

	return resp.Chapters, nil
}

// LearningPlans 获取学生维度学习计划
// 对应PHP的 Api_LearningPlan::learningPlans 方法
func (c *Client) LearningPlans(ctx *gin.Context, studentId int64, courseId int64) (*LearningPlansResponse, error) {
	// 参数验证
	if studentId <= 0 || courseId <= 0 {
		zlog.Warnf(ctx, "LearningPlans invalid params: studentId=%d, courseId=%d", studentId, courseId)
		return nil, nil
	}

	// 构建请求参数
	req := map[string]interface{}{
		"student_id": studentId,
		"course_id":  courseId,
	}

	// 设置请求选项
	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeJson}
	utils.DecorateHttpOptions(ctx, &opts)

	// 发送请求
	res, err := c.cli.HttpPost(ctx, learningPlansApi, opts)
	if err != nil {
		zlog.Warnf(ctx, "LearningPlans request err: %v", err)
		return nil, err
	}

	// 解析响应
	var resp LearningPlansResponse
	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return nil, err
	}

	return &resp, nil
}
