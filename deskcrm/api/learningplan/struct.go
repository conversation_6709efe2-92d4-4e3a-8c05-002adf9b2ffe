package learningplan

// ChapterStatRequest 章节必看状态请求参数
type ChapterStatRequest struct {
	StudentId int64   `json:"student_id"`
	CourseIds []int64 `json:"course_ids"`
}

type Chapter struct {
	StudentId int64         `json:"student_id"`
	Chapters  []ChapterStat `json:"chapters"`
}

// ChapterStat 章节必看状态
type ChapterStat struct {
	LessonId   int  `json:"lesson_id"`
	IsRequired bool `json:"isRequired"`
}

// LearningPlansRequest 学习计划请求参数
type LearningPlansRequest struct {
	StudentId int64 `json:"student_id"`
	CourseId  int64 `json:"course_id"`
}

// LearningPlansResponse 学习计划响应
type LearningPlansResponse struct {
	IsEmpty bool   `json:"isEmpty"`
	Plans   []Plan `json:"plans"`
}

// Plan 学习计划
type Plan struct {
	SubLessons []SubLesson `json:"sub_lessons"`
}

// SubLesson 子章节
type SubLesson struct {
	SubLessonId int64 `json:"sub_lesson_id"`
	Tiers       int   `json:"tiers"`
}
