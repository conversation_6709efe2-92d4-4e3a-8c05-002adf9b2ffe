package lessonDataFunc

import (
	"deskcrm/api/moat"
	"fmt"
	"net/url"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

// formatDuration 格式化时长为"XminYs"格式
// 对应PHP中的AssistantDesk_Tools::formatDurationTime方法
func FormatDuration(seconds int64) string {
	remainingSeconds := seconds % 60
	minutes := seconds / 60
	retTime := fmt.Sprintf("%dmin", minutes)
	if remainingSeconds > 0 {
		retTime = retTime + fmt.Sprintf("%ds", remainingSeconds)
	}
	return retTime
}

// LessonDataArray 创建新的课程数据数组，对应PHP中的数组格式 [显示文本, 颜色, 是否可点击]
// 例如：$row['preview'] = ['-', 'gray', 1]
func LessonDataArray(text, color string, clickable int) []interface{} {
	return []interface{}{text, color, clickable}
}

// GenerateMonthlyExamReportUrl 生成月考报告URL
// 对应PHP中的Service_Data_MonthlyExamReport::getReportUrl方法
func GenerateMonthlyExamReportUrl(ctx *gin.Context, studentUid, courseId, lessonId, mainSubjectId int64) (string, error) {
	if studentUid <= 0 || courseId <= 0 || lessonId <= 0 || mainSubjectId <= 0 {
		return "", nil
	}

	// 构建查询参数
	queryParams := url.Values{}
	queryParams.Set("studentId", cast.ToString(studentUid))
	queryParams.Set("courseId", cast.ToString(courseId))
	queryParams.Set("lessonId", cast.ToString(lessonId))
	queryParams.Set("subjectId", cast.ToString(mainSubjectId))

	// 构建基础URL - 对应PHP中的AssistantDesk_Config::PUBLIC_ADDRESS
	baseUrl := "https://www.zuoyebang.com"
	reportUrl := fmt.Sprintf("%s/assistantweb/wxview/exam-report?%s", baseUrl, queryParams.Encode())

	moatClient := moat.NewClient()
	shortUrl, err := moatClient.GetShortUrl(ctx, reportUrl)
	if err != nil {
		return reportUrl, err // 失败时返回原链接
	}

	return shortUrl, nil
}
