package dataQuery

import (
	"deskcrm/api/jxdascore"
	"time"

	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

// GetStudentLessonsScore 获取学生多章节学分信息
// 对应PHP中的 Api_Jxdascore::getStudentLessonsScore 方法
func (s *Singleton) GetStudentLessonsScore(ctx *gin.Context, courseId, studentUid int64) (map[int64]*jxdascore.StudentLessonScore, error) {
	if studentUid <= 0 || courseId <= 0 {
		return make(map[int64]*jxdascore.StudentLessonScore), nil
	}

	courseInfo, err := s.GetCourseInfo(ctx, courseId)
	if err != nil {
		return nil, err
	}

	// 获取已经结束章节的id
	lessonIds := make([]int64, 0)
	for lessonIdStr, lessonInfo := range courseInfo.LessonList {
		if cast.ToInt64(lessonInfo.StopTime) <= time.Now().Unix() {
			lessonIds = append(lessonIds, cast.ToInt64(lessonIdStr))
		}
	}

	client := jxdascore.NewClient()
	data, err := client.GetStudentLessonsScore(ctx, studentUid, lessonIds)
	if err != nil {
		return nil, err
	}

	return data, nil
}

// GetStudentsLessonScore 获取多学生单章节学分信息
// 对应PHP中的 Api_Jxdascore::getStudentsLessonScore 方法
func (s *Singleton) GetStudentsLessonScore(ctx *gin.Context, studentUids []int64, lessonId int64) (map[int64]*jxdascore.StudentLessonScore, error) {
	if len(studentUids) == 0 || lessonId <= 0 {
		return make(map[int64]*jxdascore.StudentLessonScore), nil
	}

	client := jxdascore.NewClient()
	data, err := client.GetStudentsLessonScore(ctx, studentUids, lessonId)
	if err != nil {
		zlog.Warnf(ctx, "GetStudentsLessonScore failed: %v", err)
		return nil, err
	}

	return data, nil
}
