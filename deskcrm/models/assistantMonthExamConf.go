package models

import (
	"deskcrm/consts"
	"deskcrm/helpers"

	"github.com/gin-gonic/gin"
)

// 月考报告配置常量
const (
	// 删除状态
	MonthExamConfDeletedNo = 0 // 未删除
	MonthExamConfDeletedOk = 1 // 已删除

	// 配置类型
	MonthExamConfTypeProduce  = 1 // 建议话术
	MonthExamConfTypeSendword = 2 // 寄语话术

	// 教材版本
	MonthExamConfBookVerNormal  = 57   // 通用版
	MonthExamConfBookVerBeishi  = 58   // 北师版
	MonthExamConfBookVerSujiao  = 60   // 苏教版
	MonthExamConfBookVerRenjiao = 77   // 人教版
	MonthExamConfBookVerBubian  = 3173 // 部编本

	// 班型常量
	MonthExamConfClassType1 = 1 // 默认班型
)

// AssistantMonthExamConf 月考报告配置表结构
type AssistantMonthExamConf struct {
	ID            int64  `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	GradeId       int    `gorm:"column:grade_id" json:"gradeId"`
	Subject       int    `gorm:"column:subject" json:"subject"`
	ClassType     int    `gorm:"column:class_type" json:"classType"`
	BookVer       int    `gorm:"column:book_ver" json:"bookVer"`
	Year          int    `gorm:"column:year" json:"year"`
	SeasonId      int    `gorm:"column:season_id" json:"seasonId"`
	LessonIndex   int    `gorm:"column:lesson_index" json:"lessonIndex"`
	SendWord      string `gorm:"column:send_word" json:"sendWord"`
	Type          int    `gorm:"column:type" json:"type"`
	WordOperation string `gorm:"column:word_operation" json:"wordOperation"`
	Deleted       int    `gorm:"column:deleted" json:"deleted"`
	CreateTime    int64  `gorm:"column:create_time" json:"createTime"`
	UpdateTime    int64  `gorm:"column:update_time" json:"updateTime"`
}

// TableName 指定表名
func (AssistantMonthExamConf) TableName() string {
	return "tblAssistantMonthExamConf"
}

// AssistantMonthExamConfDao 月考报告配置数据访问对象
type assistantMonthExamConfDao struct{}

var AssistantMonthExamConfDao = &assistantMonthExamConfDao{}

// GetMonthlyExamReportLessons 获取有月考报告配置的课程章节
// 对应PHP中的Service_Data_MonthlyExamReport::getMonthlyExamReportLesson方法
func (dao *assistantMonthExamConfDao) GetMonthlyExamReportLessons(ctx *gin.Context, courseId int64, lessonIds []int64, mainSubjectId, year, season, classType, bookVer int64) (map[int64]bool, error) {
	result := make(map[int64]bool)

	if courseId <= 0 || len(lessonIds) == 0 {
		return result, nil
	}

	// 映射classType
	classType = consts.GetClassType(classType)

	// 构建章节索引映射（从1开始）
	lessonIndexMap := make(map[int64]int)
	for i, lessonId := range lessonIds {
		lessonIndexMap[lessonId] = i + 1
	}

	// 构建查询条件
	var configs []AssistantMonthExamConf
	db := helpers.MysqlClient.WithContext(ctx)

	// 构建lesson_index的IN条件
	var lessonIndexes []int
	for _, index := range lessonIndexMap {
		lessonIndexes = append(lessonIndexes, index)
	}

	err := db.Where("subject = ? AND year = ? AND season_id = ? AND type = ? AND deleted = ?",
		mainSubjectId, year, season, MonthExamConfTypeProduce, MonthExamConfDeletedNo).
		Where("book_ver IN (?, ?)", MonthExamConfBookVerNormal, bookVer).
		Where("class_type IN (?, ?)", MonthExamConfClassType1, classType).
		Where("lesson_index IN ?", lessonIndexes).
		Find(&configs).Error

	if err != nil {
		return result, err
	}

	// 构建返回结果：lessonId -> 是否有配置
	indexToLessonId := make(map[int]int64)
	for lessonId, index := range lessonIndexMap {
		indexToLessonId[index] = lessonId
	}

	for _, config := range configs {
		if lessonId, exists := indexToLessonId[config.LessonIndex]; exists {
			result[lessonId] = true
		}
	}

	return result, nil
}
