package define

func GetGradeStage() map[int]string {
	return map[int]string{
		1:   "小学",
		11:  "一年级",
		12:  "二年级",
		13:  "三年级",
		14:  "四年级",
		15:  "五年级",
		16:  "六年级",
		2:   "初一",
		3:   "初二",
		4:   "初三",
		21:  "预初",
		20:  "初中",
		5:   "高一",
		6:   "高二",
		7:   "高三",
		30:  "高中", //答疑，直播
		31:  "职高一",
		32:  "职高二",
		33:  "职高三",
		50:  "高中", //题库
		60:  "学前",
		61:  "学前班",
		62:  "大班",
		63:  "中班",
		64:  "小班",
		255: "其他",
		70:  "成人", //学段
		71:  "成人", //学部下年级
		80:  "大学",
		81:  "大一",
		82:  "大二",
		83:  "大三",
		84:  "大四",
		100: "研究生",
		90:  "低幼", //学段
		91:  "低幼", //学段下年级
		92:  "高中衔接",
	}
}

// Grade2XB 表示年级关联的学部映射关系
var Grade2XB = map[int]int{
	1:   1,
	11:  1,
	12:  1,
	13:  1,
	14:  1,
	15:  1,
	16:  1,
	2:   20,
	3:   20,
	4:   20,
	21:  20,
	20:  20,
	5:   30,
	6:   30,
	7:   30,
	30:  30,
	31:  30,
	32:  30,
	33:  30,
	60:  60,
	61:  60,
	62:  60,
	63:  60,
	64:  60,
	70:  70,
	71:  70,
	81:  80,
	82:  80,
	83:  80,
	84:  80,
	100: 80,
	90:  90,
	91:  90,
	92:  30,
}

func GetSubjectDict() map[int64]string {
	return map[int64]string{
		1:   "语文",
		2:   "数学",
		3:   "英语",
		4:   "物理",
		5:   "化学",
		6:   "生物",
		7:   "政治",
		8:   "历史",
		9:   "地理",
		10:  "兴趣课",
		11:  "思想品德",
		12:  "讲座",
		13:  "理综",
		14:  "文综",
		15:  "奥数",
		16:  "通用科学",
		17:  "口语",
		18:  "写作",
		19:  "阅读",
		20:  "词汇",
		21:  "语法",
		22:  "听力",
		23:  "综合",
		24:  "中级财会",
		25:  "初级财会",
		26:  "笔试",
		27:  "面试",
		28:  "省考",
		29:  "国考",
		30:  "普通话",
		31:  "招录笔试",
		32:  "MBA",
		33:  "思维",
		34:  "写字",
		35:  "美术",
		36:  "事业单位联考",
		37:  "CMA",
		38:  "税务师",
		39:  "专业",
		40:  "图形化",
		41:  "Python编程",
		42:  "技术",
		43:  "CPA",
		44:  "副业",
		45:  "兴趣",
		46:  "职场",
		47:  "口才",
		48:  "道德与法治",
		49:  "音乐",
		50:  "美术",
		51:  "信息技术",
		52:  "思想政治",
		53:  "素养",
		54:  "记忆力",
		55:  "逻辑力",
		56:  "专注力",
		57:  "阅读力",
		58:  "围棋",
		59:  "体能运动",
		60:  "艺术素养",
		61:  "思维逻辑",
		62:  "科技创新",
		63:  "语言文学",
		64:  "传统文化",
		65:  "社会实践",
		66:  "人文",
		67:  "语言",
		68:  "家庭教育",
		69:  "学习力",
		70:  "大科学",
		71:  "表达力",
		72:  "体育与健康",
		73:  "通用技术",
		74:  "书法",
		75:  "劳动",
		76:  "日语",
		77:  "西班牙语",
		78:  "人文素养",
		79:  "运动",
		80:  "C++",
		82:  "PythonH",
		83:  "科普",
		84:  "科学",
		85:  "人文创作",
		86:  "数理思维",
		87:  "双语素养",
		88:  "自然科学",
		89:  "实验科学",
		90:  "道法",
		91:  "应用科学",
		92:  "基础科学",
		93:  "雪球思维",
		94:  "悦读创作",
		95:  "剑桥国际",
		96:  "雪球科学",
		97:  "地球探索",
		98:  "生命奥秘",
		99:  "博古通今",
		100: "AIGC",
		101: "剑桥英语",
		102: "阅写",
		103: "思维创新",
		104: "双语",
		105: "人文阅读",
		106: "大阅读",
	}
}

const (
	GradeStagePrimary   = 1  // 小学
	GradeStageJunior    = 20 // 初中
	GradeStageSenior    = 30 // 高中
	GradeStagePreschool = 60 // 学前
)

// 章节类型常量
const (
	LessonTypeMain    = 1 // 主体章节
	LessonTypeAdvance = 2 // 提升章节
)

// 章节状态常量
const (
	LessonStatusUnfinish = 0 // 未结束
	LessonStatusFinished = 1 // 已结束（正常结束）
	LessonStatusDeleted  = 2 // 已删除（异常结束）
)
